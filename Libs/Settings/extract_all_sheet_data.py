#!/usr/bin/env python3
"""
Advanced script to extract all data from Google Sheets
Automatically detects data ranges and extracts everything
"""

import gspread
import os
from dotenv import load_dotenv
import json
import pandas as pd


def get_all_sheet_data(worksheet):
    """Extract all data from the worksheet"""
    # Get all values from the worksheet
    all_values = worksheet.get_all_values()
    
    # Convert to pandas DataFrame for easier manipulation
    df = pd.DataFrame(all_values)
    
    # Remove empty rows and columns
    df = df.replace('', None)
    df = df.dropna(how='all').dropna(axis=1, how='all')
    
    return df


def find_data_sections(df):
    """Automatically find different data sections in the spreadsheet"""
    sections = {}
    
    # Look for email configuration section
    for i, row in df.iterrows():
        for j, cell in enumerate(row):
            if cell and 'host' in str(cell).lower():
                sections['email_start'] = (i, j)
            elif cell and 'admin' in str(cell).lower() and 'email_start' in sections:
                sections['accounts_start'] = (i, j)
                break
    
    return sections


def extract_email_config(df, start_pos):
    """Extract email configuration from detected position"""
    email_config = {}
    start_row, start_col = start_pos
    
    # Extract email configuration (assuming key-value pairs)
    for i in range(start_row, min(start_row + 10, len(df))):
        if i < len(df) and start_col < len(df.columns) and (start_col + 1) < len(df.columns):
            key = df.iloc[i, start_col]
            value = df.iloc[i, start_col + 1]
            
            if key and value:
                # Clean up the key
                clean_key = str(key).strip().lower()
                if clean_key in ['host', 'mail', 'password', 'port', 'mail_goal']:
                    if clean_key == 'port':
                        try:
                            email_config[clean_key] = int(value)
                        except:
                            email_config[clean_key] = value
                    else:
                        email_config[clean_key] = str(value).strip()
    
    return email_config


def extract_accounts(df, start_pos):
    """Extract user accounts from detected position"""
    accounts = {}
    start_row, start_col = start_pos
    
    current_role = None
    
    # Extract accounts data
    for i in range(start_row, len(df)):
        if i < len(df) and start_col < len(df.columns):
            cell_value = df.iloc[i, start_col]
            
            if cell_value and str(cell_value).strip():
                role = str(cell_value).strip().lower()
                
                # Check if this is a role name
                if role in ['admin', 'dean', 'teacher', 'lecturer', 'student']:
                    current_role = role
                    accounts[current_role] = {}
                
                # If we have a current role and there's a value in the next column
                elif current_role and (start_col + 1) < len(df.columns):
                    next_cell = df.iloc[i, start_col + 1]
                    if next_cell and str(next_cell).strip():
                        # This might be username or password
                        if 'username' not in accounts[current_role]:
                            accounts[current_role]['username'] = str(next_cell).strip()
                        elif 'password' not in accounts[current_role]:
                            accounts[current_role]['password'] = str(next_cell).strip()
    
    return accounts


def extract_data_by_range(worksheet, range_name):
    """Extract data from a specific range"""
    try:
        values = worksheet.get(range_name)
        return values
    except Exception as e:
        print(f"Error extracting range {range_name}: {e}")
        return None


def main():
    """Main function to extract all data"""
    load_dotenv()
    
    try:
        # Connect to Google Sheets
        gs = gspread.service_account(os.environ.get("SERVICE_ACCOUNT_FILE"))
        sht = gs.open_by_key(os.environ.get("SHEET_KEY"))
        worksheet = sht.sheet1
        
        print(f"📊 Extracting data from: {sht.title}")
        print("=" * 60)
        
        # Method 1: Get all data as DataFrame
        df = get_all_sheet_data(worksheet)
        print("📋 RAW DATA (first 20 rows):")
        print(df.head(20).to_string())
        print("\n" + "=" * 60)
        
        # Method 2: Extract specific ranges
        print("📧 EMAIL CONFIG RANGE (B3:C7):")
        email_range = extract_data_by_range(worksheet, 'B3:C7')
        if email_range:
            for row in email_range:
                if len(row) >= 2:
                    print(f"  {row[0]}: {row[1]}")
        
        print("\n👥 ACCOUNTS RANGE (B10:C17):")
        accounts_range = extract_data_by_range(worksheet, 'B10:C17')
        if accounts_range:
            for row in accounts_range:
                if len(row) >= 2:
                    print(f"  {row[0]}: {row[1]}")
        
        # Method 3: Extract all values in a structured way
        print("\n🔍 ALL WORKSHEET DATA:")
        all_values = worksheet.get_all_values()
        
        structured_data = {
            'email_config': {},
            'accounts': {},
            'raw_data': all_values
        }
        
        # Parse email config (rows 2-6, assuming 0-indexed)
        if len(all_values) > 6:
            for i in range(2, 7):  # rows 3-7 (0-indexed: 2-6)
                if i < len(all_values) and len(all_values[i]) > 2:
                    key = all_values[i][1]  # column B
                    value = all_values[i][2]  # column C
                    if key and value:
                        structured_data['email_config'][key] = value
        
        # Parse accounts (rows 9-16, assuming 0-indexed)
        if len(all_values) > 16:
            current_role = None
            for i in range(9, 17):  # rows 10-17 (0-indexed: 9-16)
                if i < len(all_values) and len(all_values[i]) > 2:
                    role_or_field = all_values[i][1]  # column B
                    value = all_values[i][2]  # column C
                    
                    if role_or_field and value:
                        # Check if this is a new role
                        if role_or_field.lower() in ['admin', 'dean', 'teacher', 'student']:
                            current_role = role_or_field.lower()
                            structured_data['accounts'][current_role] = {'username': value}
                        elif current_role and role_or_field == '':
                            # This is likely a password for the current role
                            structured_data['accounts'][current_role]['password'] = value
        
        # Display structured data
        print("\n📊 STRUCTURED DATA:")
        print("Email Config:")
        for key, value in structured_data['email_config'].items():
            print(f"  {key}: {value}")
        
        print("\nAccounts:")
        for role, creds in structured_data['accounts'].items():
            print(f"  {role}:")
            for field, value in creds.items():
                print(f"    {field}: {value}")
        
        # Save to JSON
        with open('extracted_sheet_data.json', 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Data saved to extracted_sheet_data.json")
        print("✅ Extraction completed successfully!")
        
        return structured_data
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None


if __name__ == "__main__":
    main()
