*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Admin
    [Documentation]    Test successful login with admin credentials and verify admin-specific features
    ...                Verifies that admin user can login successfully, see welcome message,
    ...                access admin menus, and logout properly
    [Tags]    login    admin    positive    smoke    critical
    Open Browser    ${URL}    ${BROWSER}

    Login With Credentials    ${ADMIN_USER}    ${ADMIN_PASS}
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, admin

    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Quản trị viên !

    # Kiể<PERSON> tra sự tồn tại của các menu dành cho admin
    Element Should Be Visible    ${H1_DASHBOARD}
    Element Should Be Visible    ${MENU_ADMIN}
    Element Should Be Visible    ${TEXT_MENU_ADMIN}

    # Thực hiện logout
    Logout Normal
    [Teardown]    Close Browser


Login As Dean
    [Documentation]    Test successful login with dean credentials and verify role-based access
    ...                Verifies that dean user can login successfully, see appropriate welcome message,
    ...                but cannot access admin-specific menus
    [Tags]    login    dean    positive    role-based    regression
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${DEAN_USER}    ${DEAN_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check user info dean
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, Trần Văn Khoa

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    [Teardown]    Close Browser


Login As Lecturer
    [Documentation]    Test successful login with lecturer credentials and verify lecturer permissions
    ...                Verifies that lecturer user can login successfully, see lecturer welcome message,
    ...                but cannot access admin-specific menus
    [Tags]    login    lecturer    positive    role-based    regression
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${LECTURER_USER}    ${LECTURER_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check notify
    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Giảng viên
    Sleep    1s

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    [Teardown]    Close Browser


Login As invalid Account
    [Documentation]    Test login failure with invalid credentials and verify error handling
    ...                Verifies that invalid login attempts show appropriate error messages
    ...                and do not grant access to protected areas
    [Tags]    login    negative    security    error-handling    critical
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${INVALID_USER}    ${INVALID_PASS}

    # Check notify
    Wait Until Element Is Visible    ${NOTIFY_LOGIN_FAILD}    10s
    ${TEXT}=    Get Text    ${NOTIFY_LOGIN_FAILD}
    Should Contain    ${TEXT}    ${NOTIFY_LOGIN_FAILD_TEXT}

    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${H1_DASHBOARD}
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Check for login element that should exist
    Element Should Be Visible    ${USERNAME_INPUT}
    Element Should Be Visible    ${PASSWORD_INPUT}
    Element Should Be Visible    ${LOGIN_BUTTON}

    # Exe logout
    [Teardown]    Close Browser

