*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/LoginLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
Login As Admin
    Open Browser    ${URL}    ${BROWSER}

    Login With Credentials    ${ADMIN_USER}    ${ADMIN_PASS}
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, admin

    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Quản trị viên !

    # <PERSON><PERSON><PERSON> tra sự tồn tại của các menu dành cho admin
    Element Should Be Visible    ${H1_DASHBOARD}
    Element Should Be Visible    ${MENU_ADMIN}
    Element Should Be Visible    ${TEXT_MENU_ADMIN}

    # Thực hiện logout
    Logout Normal
    [Teardown]    Close Browser


Login As Dean
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${DEAN_USER}    ${DEAN_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check user info dean
    ${USER_INFO_TEXT}=    Get Text    ${USER_INFO}
    Should Be Equal    ${USER_INFO_TEXT}    Xin chào, Trần Văn Khoa

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    [Teardown]    Close Browser


Login As Lecturer
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${LECTURER_USER}    ${LECTURER_PASS}
    Wait Until Element Is Visible    ${USER_INFO}    10s
    ${COOKIE_COUNT}=    Get Cookie Count
    Should Be True    ${COOKIE_COUNT} > 3

    # Check notify
    ${NOTIFY_TEXT}=    Get Text    ${NOTIFY_LOGIN}
    Should Contain    ${NOTIFY_TEXT}    Chào mừng Giảng viên
    Sleep    1s

    Element Should Be Visible    ${H1_DASHBOARD}
    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Exe logout
    Logout Normal
    [Teardown]    Close Browser


Login As invalid Account
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${INVALID_USER}    ${INVALID_PASS}

    # Check notify
    Wait Until Element Is Visible    ${NOTIFY_LOGIN_FAILD}    10s
    ${TEXT}=    Get Text    ${NOTIFY_LOGIN_FAILD}
    Should Contain    ${TEXT}    ${NOTIFY_LOGIN_FAILD_TEXT}

    # Check for admin menus that should not exist
    Element Should Not Be Visible    ${H1_DASHBOARD}
    Element Should Not Be Visible    ${MENU_ADMIN}
    Element Should Not Be Visible    ${TEXT_MENU_ADMIN}

    # Check for login element that should exist
    Element Should Be Visible    ${USERNAME_INPUT}
    Element Should Be Visible    ${PASSWORD_INPUT}
    Element Should Be Visible    ${LOGIN_BUTTON}

    # Exe logout
    [Teardown]    Close Browser
