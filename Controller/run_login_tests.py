#!/usr/bin/env python3
"""
Script để chạy test login và gửi email báo cáo kết quả
"""

import os
import subprocess
import sys
import smtplib
import xml.etree.ElementTree as ET
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders

# Email configuration
EMAIL_CONFIG = {
    'host': 'smtp.gmail.com',
    'user': '<EMAIL>',
    'password': 'jwzrpcwzvmvnbzxy',
    'port': 587
}

def run_robot_test(test_file):
    """Chạy file test Robot Framework"""
    print(f"\n{'='*60}")
    print(f"🚀 Đang chạy test: {test_file}")
    print(f"{'='*60}")

    try:
        # Chạy robot command
        result = subprocess.run([
            'robot',
            '--outputdir', 'Tests/Login/results',
            f'Tests/Login/{test_file}'
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print(f"✅ {test_file} - PASSED")
            return True, result.stdout
        else:
            print(f"❌ {test_file} - FAILED")
            print(f"Error: {result.stderr}")
            return False, result.stderr

    except Exception as e:
        print(f"❌ Lỗi khi chạy {test_file}: {str(e)}")
        return False, str(e)

def main():
    """Hàm chính để chạy test login và gửi email báo cáo"""
    start_time = datetime.now()
    print("🤖 Robot Framework Login Test Runner")
    print(f"⏰ Thời gian bắt đầu: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Tạo thư mục results nếu chưa có
    results_dir = "Tests/Login/results"
    os.makedirs(results_dir, exist_ok=True)

    # File test login (chứa 4 test cases)
    test_file = "LoginTest.robot"

    # Kiểm tra file test có tồn tại không
    if not os.path.exists(f"Tests/Login/{test_file}"):
        print(f"⚠️  Không tìm thấy file: {test_file}")
        return False

    # Chạy test
    success, output = run_robot_test(test_file)
    end_time = datetime.now()

    # Parse kết quả từ output.xml
    output_file = f"{results_dir}/output.xml"
    test_results = parse_robot_results(output_file) if os.path.exists(output_file) else None

    # Hiển thị tổng kết
    print(f"\n{'='*60}")
    print("📊 TỔNG KẾT KẾT QUẢ")
    print(f"{'='*60}")

    if test_results:
        print(f"📈 Tổng số test cases: {test_results['total']}")
        print(f"✅ Passed: {test_results['passed']}")
        print(f"❌ Failed: {test_results['failed']}")

        print(f"\n📋 Chi tiết test cases:")
        for test in test_results['details']:
            status_icon = "✅" if test['status'] == 'PASS' else "❌"
            print(f"  {test['name']:<30} - {status_icon} {test['status']}")
    else:
        print("❌ Không thể parse kết quả test")

    print(f"\n⏰ Thời gian kết thúc: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 Kết quả chi tiết tại: {results_dir}/")

    # Gửi email báo cáo
    print(f"\n📧 Đang gửi email báo cáo...")
    email_sent = send_email_report(
        test_results,
        start_time.strftime('%Y-%m-%d %H:%M:%S'),
        end_time.strftime('%Y-%m-%d %H:%M:%S')
    )

    if not email_sent:
        print("⚠️  Không thể gửi email báo cáo")

    return success and (test_results['failed'] == 0 if test_results else False)

def parse_robot_results(output_file):
    """Parse kết quả từ file output.xml của Robot Framework"""
    try:
        tree = ET.parse(output_file)
        root = tree.getroot()

        # Lấy thông tin tổng quan
        suite = root.find('suite')

        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        test_details = []

        # Đếm test cases
        for test in suite.findall('.//test'):
            total_tests += 1
            test_name = test.get('name')
            test_status = test.find('status')
            status = test_status.get('status')

            if status == 'PASS':
                passed_tests += 1
            else:
                failed_tests += 1

            test_details.append({
                'name': test_name,
                'status': status,
                'message': test_status.text if test_status.text else ''
            })

        return {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'details': test_details
        }
    except Exception as e:
        print(f"Lỗi khi parse kết quả: {e}")
        return None

def send_email_report(test_results, start_time, end_time):
    """Gửi email báo cáo kết quả test"""
    try:
        # Tạo email
        msg = MIMEMultipart()
        msg['From'] = EMAIL_CONFIG['user']
        msg['To'] = EMAIL_CONFIG['user']  # Gửi cho chính mình
        msg['Subject'] = f"🤖 Robot Framework Test Report - {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        # Tạo nội dung email
        if test_results:
            status_icon = "✅" if test_results['failed'] == 0 else "❌"
            body = f"""
            <html>
            <body>
                <h2>{status_icon} Robot Framework Login Test Report</h2>
                <p><strong>Thời gian chạy:</strong> {start_time} - {end_time}</p>

                <h3>📊 Tổng kết:</h3>
                <ul>
                    <li>Tổng số test: {test_results['total']}</li>
                    <li>✅ Passed: {test_results['passed']}</li>
                    <li>❌ Failed: {test_results['failed']}</li>
                </ul>

                <h3>📋 Chi tiết test cases:</h3>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr style="background-color: #f2f2f2;">
                        <th style="padding: 8px;">Test Case</th>
                        <th style="padding: 8px;">Status</th>
                        <th style="padding: 8px;">Message</th>
                    </tr>
            """

            for test in test_results['details']:
                status_icon = "✅" if test['status'] == 'PASS' else "❌"
                body += f"""
                    <tr>
                        <td style="padding: 8px;">{test['name']}</td>
                        <td style="padding: 8px;">{status_icon} {test['status']}</td>
                        <td style="padding: 8px;">{test['message']}</td>
                    </tr>
                """

            body += """
                </table>
                <br>
                <p><em>Report được tạo tự động bởi Robot Framework Test Runner</em></p>
            </body>
            </html>
            """
        else:
            body = """
            <html>
            <body>
                <h2>❌ Robot Framework Test Report</h2>
                <p>Có lỗi xảy ra khi chạy test hoặc parse kết quả.</p>
                <p><em>Vui lòng kiểm tra logs để biết thêm chi tiết.</em></p>
            </body>
            </html>
            """

        msg.attach(MIMEText(body, 'html'))

        # Đính kèm file report.html nếu có
        report_file = "Tests/Login/results/report.html"
        if os.path.exists(report_file):
            with open(report_file, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= "robot_report_{datetime.now().strftime("%Y%m%d_%H%M")}.html"'
                )
                msg.attach(part)

        # Gửi email
        server = smtplib.SMTP(EMAIL_CONFIG['host'], EMAIL_CONFIG['port'])
        server.starttls()
        server.login(EMAIL_CONFIG['user'], EMAIL_CONFIG['password'])
        text = msg.as_string()
        server.sendmail(EMAIL_CONFIG['user'], EMAIL_CONFIG['user'], text)
        server.quit()

        print("📧 Email báo cáo đã được gửi thành công!")
        return True

    except Exception as e:
        print(f"❌ Lỗi khi gửi email: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
